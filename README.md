# EkoMarineServices Web Sitesi

Uluslararası ortaklıklarla Suzuki tekne bakım ve onarım hizmetlerinde uzmanlaşmış EkoMarineServices için profesyonel bir web sitesi.

## Özellikler

### 🏠 Ana Sayfa
- Şirket tanıtımı ile profesyonel hero bölümü
- Temel hizmetleri vurgulayan özellik kartları
- Akıcı animasyonlar ve modern tasarım
- Mobil uyumlu düzen

### 📸 Galeri
- img klasöründeki tüm medya dosyalarını gösteren dinamik galeri
- Hem resim (JPG) hem de video (MP4) desteği
- Tam ekran medya görüntüleme için modal görünüm
- Optimal performans için lazy loading

### ℹ️ Hakkımızda
- Şirket bilgileri ve geçmişi
- Hizmet vurguları ve sertifikalar
- Uzmanlığı sergileyen profesyonel görüntüler

### 🏆 Başarılar/Başarı Hikayeleri
- İş başarıları için özel bölüm
- Belirli ortaklık resimlerini içerir:
  - "İşler güçler (1).jpg" - Japon ortaklık anlaşması
  - "İşler güçler.jpg" - İş mükemmelliği tanınması
- Uluslararası ilişkilerin profesyonel sunumu

### 📞 İletişim
- İletişim bilgileri bölümü
- Profesyonel iletişim detayları düzeni
- Kolay bulunabilir iletişim yöntemleri

## Teknik Özellikler

### Kullanılan Teknolojiler
- **HTML5** - Modern semantik işaretleme
- **CSS3** - Özel özellikler ve animasyonlarla gelişmiş stil
- **JavaScript (ES6+)** - Etkileşimli işlevsellik ve dinamik içerik
- **Bootstrap 5.3** - Duyarlı framework ve bileşenler
- **Font Awesome 6.4** - Profesyonel simgeler

### Temel Özellikler
- **Duyarlı Tasarım** - Tüm cihazlarda mükemmel çalışır
- **Modern Animasyonlar** - Akıcı geçişler ve hover efektleri
- **Performans Optimize** - Lazy loading ve verimli kod
- **SEO Dostu** - Semantik HTML ve uygun meta etiketleri
- **Çapraz Tarayıcı Uyumlu** - Tüm modern tarayıcılarda çalışır

### Dosya Yapısı
```
ekomarineservice/
├── index.html          # Ana HTML dosyası
├── css/
│   └── style.css       # Özel CSS stilleri
├── js/
│   └── script.js       # JavaScript işlevselliği
├── img/                # Medya dosyaları klasörü
│   ├── #işlergüçler#EkoMarineServices# (1).jpg
│   ├── #işlergüçler#EkoMarineServices# (2).jpg
│   ├── İşler güçler (1).jpg
│   ├── İşler güçler (1).mp4
│   ├── İşler güçler.jpg
│   └── İşler güçler.mp4
└── README.md           # Bu dosya
```

## Nasıl Kullanılır

### Seçenek 1: Doğrudan Dosya Açma
1. Varsayılan tarayıcınızda açmak için `index.html` dosyasına çift tıklayın
2. Sunucu gerektiren bazı gelişmiş JavaScript özellikleri dışında tüm özellikler çalışacaktır

### Seçenek 2: Yerel Sunucu (Önerilen)
1. **Python kullanarak** (yüklüyse):
   ```bash
   python -m http.server 8000
   ```
   Ardından tarayıcınızda `http://localhost:8000` adresini açın

2. **Node.js kullanarak** (yüklüyse):
   ```bash
   npx serve .
   ```

3. **PHP kullanarak** (yüklüyse):
   ```bash
   php -S localhost:8000
   ```

### Seçenek 3: Live Server Uzantısı
VS Code kullanıyorsanız, "Live Server" uzantısını yükleyin ve `index.html` dosyasına sağ tıklayarak "Open with Live Server" seçeneğini seçin

## Özelleştirme

### Yeni Medya Dosyaları Ekleme
1. Yeni JPG veya MP4 dosyalarını `img/` klasörüne yerleştirin
2. Yeni dosyaları dahil etmek için `js/script.js` dosyasındaki `mediaFiles` dizisini güncelleyin
3. Tutarlılık için mevcut formatı takip edin

### İçerik Güncelleme
- **Şirket Bilgileri**: `index.html` dosyasındaki metin içeriğini düzenleyin
- **Stil**: Görsel değişiklikler için `css/style.css` dosyasını değiştirin
- **İşlevsellik**: Davranışsal değişiklikler için `js/script.js` dosyasını güncelleyin

### Renk Şeması
Web sitesi profesyonel denizcilik temalı renk paleti kullanır:
- Birincil: `#0066cc` (Okyanus Mavisi)
- İkincil: `#004499` (Koyu Mavi)
- Vurgu: `#00aaff` (Açık Mavi)

## Tarayıcı Desteği
- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## Performans Özellikleri
- Resimler için lazy loading
- Optimize edilmiş animasyonlar
- Verimli JavaScript
- Sıkıştırılmış varlıklar
- Mobil öncelikli duyarlı tasarım

## İletişim Bilgileri
Teknik destek veya özelleştirme talepleri için lütfen geliştirme ekibiyle iletişime geçin.

---

**EkoMarineServices** - Profesyonel Denizcilik Hizmetleri | Suzuki Uzmanı
