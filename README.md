# EkoMarineServices Website

A professional website for EkoMarineServices, specializing in Suzuki boat maintenance and repair services with international partnerships.

## Features

### 🏠 Homepage
- Professional hero section with company introduction
- Feature cards highlighting key services
- Smooth animations and modern design
- Mobile-responsive layout

### 📸 Gallery
- Dynamic gallery displaying all media files from the img folder
- Support for both images (JPG) and videos (MP4)
- Modal view for fullscreen media viewing
- Lazy loading for optimal performance

### ℹ️ About Us
- Company information and background
- Service highlights and certifications
- Professional imagery showcasing expertise

### 🏆 Achievements/Success Stories
- Dedicated section for business achievements
- Features specific partnership images:
  - "<PERSON>şler güçler (1).jpg" - Japanese partnership agreement
  - "İşler güçler.jpg" - Business excellence recognition
- Professional presentation of international relationships

### 📞 Contact
- Contact information section
- Professional contact details layout
- Easy-to-find contact methods

## Technical Specifications

### Technologies Used
- **HTML5** - Modern semantic markup
- **CSS3** - Advanced styling with custom properties and animations
- **JavaScript (ES6+)** - Interactive functionality and dynamic content
- **Bootstrap 5.3** - Responsive framework and components
- **Font Awesome 6.4** - Professional icons

### Key Features
- **Responsive Design** - Works perfectly on all devices
- **Modern Animations** - Smooth transitions and hover effects
- **Performance Optimized** - Lazy loading and efficient code
- **SEO Friendly** - Semantic HTML and proper meta tags
- **Cross-browser Compatible** - Works on all modern browsers

### File Structure
```
ekomarineservice/
├── index.html          # Main HTML file
├── css/
│   └── style.css       # Custom CSS styles
├── js/
│   └── script.js       # JavaScript functionality
├── img/                # Media files folder
│   ├── #işlergüçler#EkoMarineServices# (1).jpg
│   ├── #işlergüçler#EkoMarineServices# (2).jpg
│   ├── İşler güçler (1).jpg
│   ├── İşler güçler (1).mp4
│   ├── İşler güçler.jpg
│   └── İşler güçler.mp4
└── README.md           # This file
```

## How to Use

### Option 1: Direct File Opening
1. Simply double-click on `index.html` to open in your default browser
2. All features will work except for some advanced JavaScript features that require a server

### Option 2: Local Server (Recommended)
1. **Using Python** (if installed):
   ```bash
   python -m http.server 8000
   ```
   Then open `http://localhost:8000` in your browser

2. **Using Node.js** (if installed):
   ```bash
   npx serve .
   ```

3. **Using PHP** (if installed):
   ```bash
   php -S localhost:8000
   ```

### Option 3: Live Server Extension
If using VS Code, install the "Live Server" extension and right-click on `index.html` to select "Open with Live Server"

## Customization

### Adding New Media Files
1. Place new JPG or MP4 files in the `img/` folder
2. Update the `mediaFiles` array in `js/script.js` to include new files
3. Follow the existing format for consistency

### Updating Content
- **Company Information**: Edit the text content in `index.html`
- **Styling**: Modify `css/style.css` for visual changes
- **Functionality**: Update `js/script.js` for behavioral changes

### Color Scheme
The website uses a professional marine-themed color palette:
- Primary: `#0066cc` (Ocean Blue)
- Secondary: `#004499` (Deep Blue)
- Accent: `#00aaff` (Light Blue)

## Browser Support
- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## Performance Features
- Lazy loading for images
- Optimized animations
- Efficient JavaScript
- Compressed assets
- Mobile-first responsive design

## Contact Information
For technical support or customization requests, please contact the development team.

---

**EkoMarineServices** - Professional Marine Services | Suzuki Specialist
