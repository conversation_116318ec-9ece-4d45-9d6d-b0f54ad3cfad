# Netlify Configuration for EkoMarineServices Website

[build]
  # No build command needed for static site
  command = ""
  # Publish the root directory
  publish = "."

[build.environment]
  # No environment variables needed

# Headers for better performance and security
[[headers]]
  for = "/*"
  [headers.values]
    # Security headers
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    
    # Performance headers
    Cache-Control = "public, max-age=********"

# Specific headers for HTML files
[[headers]]
  for = "/*.html"
  [headers.values]
    Cache-Control = "public, max-age=3600"

# Headers for CSS and JS files
[[headers]]
  for = "/css/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "/js/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Headers for images and videos
[[headers]]
  for = "/img/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Single Page Application (SPA) redirect
# This ensures all routes redirect to index.html for client-side routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Form handling (for future contact forms)
# [[forms]]
#   name = "contact"
