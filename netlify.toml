# Netlify Configuration for EkoMarineServices Website

[build]
  # No build command needed for static site
  command = ""
  # Publish the root directory
  publish = "."

[build.environment]
  # No environment variables needed

# Headers for better performance and security
[[headers]]
  for = "/*"
  [headers.values]
    # Security headers
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    
    # Performance headers
    Cache-Control = "public, max-age=31536000"

# Specific headers for HTML files
[[headers]]
  for = "/*.html"
  [headers.values]
    Cache-Control = "public, max-age=3600"

# Headers for CSS and JS files
[[headers]]
  for = "/css/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/js/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Headers for images and videos
[[headers]]
  for = "/img/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Redirects (if needed in the future)
# [[redirects]]
#   from = "/old-page"
#   to = "/new-page"
#   status = 301

# Form handling (for future contact forms)
# [[forms]]
#   name = "contact"

# Error pages
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  conditions = {Role = ["admin"]}

# 404 page redirect to home
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 404
