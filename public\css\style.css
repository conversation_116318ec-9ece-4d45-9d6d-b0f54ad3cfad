/* Custom CSS for EkoMarineServices */

:root {
    --primary-color: #0066cc;
    --secondary-color: #004499;
    --accent-color: #00aaff;
    --text-dark: #333333;
    --text-light: #666666;
    --bg-light: #f8f9fa;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

/* Navigation */
.navbar {
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    z-index: 1050;
}

.navbar-brand {
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    margin: 0 10px;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--accent-color) !important;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    position: relative;
    overflow: hidden;
    background-attachment: fixed;
    background-size: cover;
    background-position: center;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.05)"><path d="M0,0v46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1047.93,5.4,1100.92,13.91L1100,0Z"></path></svg>') repeat-x;
    background-size: 1000px 100px;
    animation: wave 8s linear infinite;
    z-index: 1;
}

@keyframes wave {
    0% { background-position-x: 0; }
    100% { background-position-x: 1000px; }
}

.hero-overlay {
    position: relative;
    z-index: 10;
    padding-top: 80px;
    background: rgba(0, 0, 0, 0.1);
}

.hero-features {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-top: 2rem;
}

.feature-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.feature-card h4 {
    color: white;
    margin-bottom: 1rem;
}

.feature-card p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0;
}

/* Section Spacing */
section {
    scroll-margin-top: 100px;
    position: relative;
    z-index: 5;
}

/* About Section */
.about-image img {
    border-radius: 15px;
    transition: transform 0.3s ease;
}

.about-image img:hover {
    transform: scale(1.05);
}

/* Achievements Section */
.achievement-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.achievement-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.achievement-card img {
    border-radius: 10px;
    transition: transform 0.3s ease;
}

.achievement-card:hover img {
    transform: scale(1.02);
}

.achievement-content h4 {
    color: var(--primary-color);
    font-weight: 600;
}

/* Gallery Section */
.gallery-item {
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.gallery-item img,
.gallery-item video {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-item:hover img,
.gallery-item:hover video {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0, 102, 204, 0.8), rgba(0, 170, 255, 0.8));
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-overlay i {
    color: white;
    font-size: 2rem;
}

/* Contact Section */
#contact i {
    color: var(--accent-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-features {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .feature-card {
        padding: 1.5rem;
    }

    .display-4 {
        font-size: 2.5rem;
    }

    .display-5 {
        font-size: 2rem;
    }

    .hero-section::before {
        background-size: 500px 50px;
        animation: wave 6s linear infinite;
    }
}

@media (min-width: 992px) {
    .hero-features {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading Animation */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Page Loading Overlay */
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.page-loader.hidden {
    opacity: 0;
    visibility: hidden;
}

.loader-content {
    text-align: center;
    color: white;
}

.loader-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Button Styles */
.btn {
    border-radius: 25px;
    padding: 12px 30px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}
